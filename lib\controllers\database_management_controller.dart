import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/database_management_service.dart';
import '../models/database_table_model.dart';
import 'auth_controller.dart';

/// متحكم إدارة قاعدة البيانات
/// 
/// يدير حالة واجهة إدارة قاعدة البيانات
class DatabaseManagementController extends GetxController {
  final DatabaseManagementService _service;

  DatabaseManagementController(this._service);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxString _successMessage = ''.obs;

  // البيانات
  final RxList<DatabaseTable> _tables = <DatabaseTable>[].obs;
  final Rxn<DatabaseTable> _selectedTable = Rxn<DatabaseTable>();
  final RxList<Map<String, dynamic>> _tableData = <Map<String, dynamic>>[].obs;
  final RxMap<String, dynamic> _databaseInfo = <String, dynamic>{}.obs;

  // إعدادات العرض
  final RxInt _currentPage = 1.obs;
  final RxInt _pageSize = 50.obs;
  final RxString _searchQuery = ''.obs;
  final RxString _searchText = ''.obs;
  final RxString _filterClause = ''.obs;
  final RxMap<String, dynamic> _filters = <String, dynamic>{}.obs;

  // الترتيب
  final RxString _orderBy = ''.obs;
  final RxString _orderDirection = 'ASC'.obs;

  // حالة العمليات
  final RxBool _isLoadingTables = false.obs;
  final RxBool _isLoadingData = false.obs;
  final RxBool _isCreating = false.obs;
  final RxBool _isUpdating = false.obs;
  final RxBool _isDeleting = false.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get successMessage => _successMessage.value;
  List<DatabaseTable> get tables => _tables;
  DatabaseTable? get selectedTable => _selectedTable.value;
  List<Map<String, dynamic>> get tableData => _tableData;
  Map<String, dynamic> get databaseInfo => _databaseInfo;
  int get currentPage => _currentPage.value;
  int get pageSize => _pageSize.value;
  String get searchQuery => _searchQuery.value;
  RxString get searchText => _searchText;
  RxString get filterClause => _filterClause;
  Map<String, dynamic> get filters => _filters;
  RxString get orderBy => _orderBy;
  RxString get orderDirection => _orderDirection;
  bool get isLoadingTables => _isLoadingTables.value;
  bool get isLoadingData => _isLoadingData.value;
  bool get isCreating => _isCreating.value;
  bool get isUpdating => _isUpdating.value;
  bool get isDeleting => _isDeleting.value;

  @override
  void onInit() {
    super.onInit();
    // تحميل البيانات بشكل متتالي لتجنب التحميل المتزامن
    _initializeData();
  }

  /// تهيئة البيانات
  Future<void> _initializeData() async {
    try {
      await loadDatabaseInfo();
      await loadTables();
    } catch (e) {
      debugPrint('خطأ في تهيئة البيانات: $e');
      _error.value = 'خطأ في تهيئة البيانات: $e';
    }
  }

  /// تحميل قائمة الجداول
  Future<void> loadTables() async {
    if (_isLoadingTables.value) return; // تجنب التحميل المتكرر

    _isLoadingTables.value = true;
    _error.value = '';

    try {
      final tables = await _service.getAvailableTables();
      _tables.assignAll(tables);

      // تحديد الجدول الأول كافتراضي إذا لم يكن هناك جدول محدد
      if (tables.isNotEmpty && _selectedTable.value == null) {
        selectTable(tables.first);
      } else if (tables.isEmpty) {
        _selectedTable.value = null;
        _tableData.clear();
        _error.value = 'لا توجد جداول متاحة. تحقق من الاتصال بقاعدة البيانات.';
      }

      debugPrint('تم تحميل ${tables.length} جدول');

      // مسح رسالة الخطأ إذا تم التحميل بنجاح
      if (tables.isNotEmpty) {
        _error.value = '';
      }
    } catch (e) {
      _error.value = 'خطأ في الاتصال بقاعدة البيانات. تحقق من إعدادات الشبكة.';
      debugPrint('خطأ في تحميل الجداول: $e');
      _tables.clear();
      _selectedTable.value = null;
      _tableData.clear();
    } finally {
      _isLoadingTables.value = false;
    }
  }

  /// تحميل معلومات قاعدة البيانات
  Future<void> loadDatabaseInfo() async {
    try {
      final info = _service.getDatabaseInfo();
      _databaseInfo.assignAll(info);
    } catch (e) {
      debugPrint('خطأ في تحميل معلومات قاعدة البيانات: $e');
    }
  }

  /// اختيار جدول
  void selectTable(DatabaseTable table) {
    _selectedTable.value = table;
    _currentPage.value = 1;
    _searchQuery.value = '';
    _filters.clear();
    _error.value = '';
    _successMessage.value = '';
    loadTableData();
  }

  /// تحميل بيانات الجدول المحدد
  Future<void> loadTableData() async {
    if (_selectedTable.value == null) return;
    if (_isLoadingData.value) return; // تجنب التحميل المتكرر

    _isLoadingData.value = true;
    _error.value = '';

    try {
      debugPrint('تحميل بيانات الجدول: ${_selectedTable.value!.name}');
      debugPrint('استعلام البحث: "${_searchQuery.value}"');
      debugPrint('الصفحة: ${_currentPage.value}, حجم الصفحة: ${_pageSize.value}');

      final data = await _service.getTableData(
        _selectedTable.value!.name,
        page: _currentPage.value,
        pageSize: _pageSize.value,
        searchQuery: _searchQuery.value.isEmpty ? null : _searchQuery.value,
        filters: _filters.isEmpty ? null : _filters,
      );

      _tableData.assignAll(data);
      debugPrint('تم تحميل ${data.length} سجل من ${_selectedTable.value!.name}');

      // مسح رسالة الخطأ إذا تم التحميل بنجاح
      if (data.isNotEmpty) {
        _error.value = '';
      } else {
        // إذا كانت البيانات فارغة، تحقق من السبب
        try {
          final authController = Get.find<AuthController>();
          if (!authController.isLoggedIn) {
            _error.value = 'يرجى تسجيل الدخول للوصول إلى البيانات';
          } else {
            _error.value = 'لا توجد بيانات في هذا الجدول';
          }
        } catch (e) {
          // إذا لم يتم العثور على AuthController، اعتبر أن المستخدم غير مسجل دخول
          _error.value = 'يرجى تسجيل الدخول للوصول إلى البيانات';
        }
      }
    } catch (e) {
      // تحسين رسائل الخطأ بناءً على نوع الخطأ
      if (e.toString().contains('401')) {
        _error.value = 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى';
      } else if (e.toString().contains('403')) {
        _error.value = 'ليس لديك صلاحية للوصول إلى هذا الجدول';
      } else if (e.toString().contains('404')) {
        _error.value = 'الجدول المطلوب غير موجود';
      } else if (e.toString().contains('500')) {
        _error.value = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
      } else {
        _error.value = 'لا يمكن تحميل بيانات الجدول. تحقق من الاتصال بالخادم.';
      }
      debugPrint('خطأ في تحميل بيانات الجدول: $e');
      _tableData.clear();
    } finally {
      _isLoadingData.value = false;
    }
  }

  /// إنشاء سجل جديد
  Future<bool> createRecord(Map<String, dynamic> data) async {
    if (_selectedTable.value == null) return false;

    _isCreating.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final result = await _service.createRecord(_selectedTable.value!.name, data);
      
      if (result != null) {
        _successMessage.value = 'تم إنشاء السجل بنجاح';
        await loadTableData(); // إعادة تحميل البيانات
        return true;
      } else {
        _error.value = 'فشل في إنشاء السجل';
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في إنشاء السجل: $e';
      debugPrint('خطأ في إنشاء السجل: $e');
      return false;
    } finally {
      _isCreating.value = false;
    }
  }

  /// تحديث سجل
  Future<bool> updateRecord(int id, Map<String, dynamic> data) async {
    if (_selectedTable.value == null) return false;

    _isUpdating.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final result = await _service.updateRecord(_selectedTable.value!.name, id, data);

      if (result != null) {
        _successMessage.value = 'تم تحديث السجل بنجاح';
        await loadTableData(); // إعادة تحميل البيانات
        return true;
      } else {
        _error.value = 'فشل في تحديث السجل';
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في تحديث السجل: $e';
      debugPrint('خطأ في تحديث السجل: $e');
      return false;
    } finally {
      _isUpdating.value = false;
    }
  }

  /// تحديث صف
  Future<bool> updateRow(Map<String, dynamic> data, String primaryKeyColumn) async {
    if (_selectedTable.value == null) return false;

    final primaryKeyValue = data[primaryKeyColumn];
    if (primaryKeyValue == null) return false;

    return await updateRecord(primaryKeyValue, data);
  }

  /// حذف سجل
  Future<bool> deleteRecord(int id) async {
    if (_selectedTable.value == null) return false;

    _isDeleting.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final success = await _service.deleteRecord(_selectedTable.value!.name, id);
      
      if (success) {
        _successMessage.value = 'تم حذف السجل بنجاح';
        await loadTableData(); // إعادة تحميل البيانات
        return true;
      } else {
        _error.value = 'فشل في حذف السجل';
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في حذف السجل: $e';
      debugPrint('خطأ في حذف السجل: $e');
      return false;
    } finally {
      _isDeleting.value = false;
    }
  }

  /// البحث في البيانات
  void search(String query) {
    debugPrint('تم استدعاء البحث في الكنترولر: "$query"');
    debugPrint('الجدول المحدد: ${_selectedTable.value?.name}');
    _searchQuery.value = query;
    _currentPage.value = 1;
    debugPrint('تم تعيين استعلام البحث: "${_searchQuery.value}"');
    loadTableData();
  }

  /// تطبيق فلاتر
  void applyFilters(Map<String, dynamic> filters) {
    _filters.assignAll(filters);
    _currentPage.value = 1;
    loadTableData();
  }

  /// مسح الفلاتر
  void clearFilters() {
    _filters.clear();
    _searchQuery.value = '';
    _currentPage.value = 1;
    loadTableData();
  }

  /// مسح البحث
  void clearSearch() {
    _searchQuery.value = '';
    _searchText.value = '';
    _currentPage.value = 1;
    loadTableData();
  }

  /// تطبيق البحث
  void applySearch(String column, String query) {
    _searchQuery.value = query;
    _searchText.value = query;
    _currentPage.value = 1;
    loadTableData();
  }

  /// مسح التصفية
  void clearFilter() {
    _filterClause.value = '';
    _filters.clear();
    _currentPage.value = 1;
    loadTableData();
  }

  /// تغيير الترتيب
  void changeOrder(String column, bool ascending) {
    _orderBy.value = column;
    _orderDirection.value = ascending ? 'ASC' : 'DESC';
    loadTableData();
  }

  /// الانتقال للصفحة التالية
  void nextPage() {
    _currentPage.value++;
    loadTableData();
  }

  /// الانتقال للصفحة السابقة
  void previousPage() {
    if (_currentPage.value > 1) {
      _currentPage.value--;
      loadTableData();
    }
  }

  /// الانتقال لصفحة محددة
  void goToPage(int page) {
    if (page > 0) {
      _currentPage.value = page;
      loadTableData();
    }
  }

  /// تغيير حجم الصفحة
  void changePageSize(int size) {
    _pageSize.value = size;
    _currentPage.value = 1;
    loadTableData();
  }

  /// اختبار الاتصال
  Future<bool> testConnection() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final isConnected = await _service.testConnection();
      
      if (isConnected) {
        _successMessage.value = 'تم الاتصال بقاعدة البيانات بنجاح';
        await loadTables();
        await loadDatabaseInfo();
      } else {
        _error.value = 'فشل في الاتصال بقاعدة البيانات';
      }

      return isConnected;
    } catch (e) {
      _error.value = 'خطأ في اختبار الاتصال: $e';
      debugPrint('خطأ في اختبار الاتصال: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إعادة تحميل البيانات
  @override
  Future<void> refresh() async {
    try {
      // تحميل البيانات بشكل متتالي لتجنب التداخل
      await loadDatabaseInfo();
      await loadTables();

      // تحميل بيانات الجدول المحدد إذا كان موجوداً
      if (_selectedTable.value != null) {
        await loadTableData();
      }

      debugPrint('تم تحديث جميع البيانات بنجاح');
    } catch (e) {
      debugPrint('خطأ في تحديث البيانات: $e');
      _error.value = 'خطأ في تحديث البيانات: $e';
    }
  }

  /// مسح الرسائل
  void clearMessages() {
    _error.value = '';
    _successMessage.value = '';
  }

  /// الحصول على خيارات المفتاح الخارجي
  Future<List<Map<String, dynamic>>> getForeignKeyOptions(
    String tableName,
    String keyColumn,
    String displayColumn,
  ) async {
    try {
      return await _service.getForeignKeyOptions(tableName, keyColumn, displayColumn);
    } catch (e) {
      debugPrint('خطأ في الحصول على خيارات المفتاح الخارجي: $e');
      return [];
    }
  }

  /// الحصول على قيمة عرض المفتاح الخارجي
  Future<String> getForeignKeyDisplayValue(
    String tableName,
    String keyColumn,
    String displayColumn,
    dynamic keyValue,
  ) async {
    try {
      return await _service.getForeignKeyDisplayValue(tableName, keyColumn, displayColumn, keyValue);
    } catch (e) {
      debugPrint('خطأ في الحصول على قيمة عرض المفتاح الخارجي: $e');
      return keyValue.toString();
    }
  }
}
